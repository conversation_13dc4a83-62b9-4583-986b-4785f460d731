<div>
  <style>
      me {
        position: relative;
        height: 5.9375rem;
        margin-top: 1rem;
        margin-bottom: 1rem;
        flex-basis: 100%;
      }

      me select {
        all: unset;
        height: 1.75rem;
        position: absolute;
        top: 1.875rem;
        left: 0;
        right: 0;
        font-family: 'Noto Sans', sans-serif;
        font-size: 1.125rem;
        font-weight: 400;
        color: var(--color-text-black);
        border: 0;

        background-color: transparent;
        border-bottom: 1px solid var(--color-input-lines);
        background: url(/static/images/icons8-arrow-down-64.png) no-repeat right var(--color-background-bright);
        background-size: 1.625rem 1.5rem;
        border-radius: 0;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;

        &:focus {
            outline: 0;
            border-bottom: 1px solid var(--color-input-lines);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 0.9375rem;
                color: var(--color-text-dark);
                top: 0.125rem;
                right: 2rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        &:valid:not([value=""]) {
            border-bottom: 1px solid var(--color-selected-green);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 0.9375rem;
                color: var(--color-text-dark);
                top: 0.125rem;
                right: 2rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        &:disabled {
            color: var(--color-disabled);
            border-bottom: 1px solid var(--color-disabled);
        }
      }

      me .input-label.float-label {
        font-family: 'Noto Serif', serif;
        font-style: italic;
        font-size: 0.9375rem;
        color: var(--color-text-dark);
        top: 0.125rem;
        right: 2rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      select:disabled + .input-label.float-label {
        color: var(--color-disabled);
      }

      me .input-label,
      .input-label {
          pointer-events: none;
      }

      me .input-group__error {
          position: absolute;
          top: 4.375rem;
          left: 0.625rem;
          color: var(--color-selected-red);
          display: block;
          visibility: hidden;
          opacity: 0;
          font-family: 'Noto Serif', serif;
          font-size: 0.875rem;
          transition: all 0.3s ease-out;
      }

  </style>

  <select class="custom-float-select" name="{{ namealwayschange }}" id="{{ namealwayschange }}" onchange="this.setAttribute('value', this.value);" {% if required is not defined or required %} required{% endif %} >
    <option value=""></option>
    {% for option in optionslist %}
      {% if option is mapping %}
        <option value="{{ option.value }}"{% if option.selected %} selected{% endif %}>{{ option.value }}</option>
      {% else %}
        <option value="{{ option }}">{{ option }}</option>
      {% endif %}
    {% endfor %}
  </select>
  <label class="input-label custom-float-label">
    <style>
        me {
            position: absolute;
            top: 1.75rem;
            left: 0;
            right: 2rem;
            color: var(--color-text-black);
            transition: .15s ease;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        select:disabled + .input-label {
            color: var(--color-disabled);
        }
        select:disabled + .input-label.float-label {
            color: var(--color-disabled);
        }
    </style>
    {{ label | safe }}
  </label>

  <span class="input-group__error">{{ errormessage }}</span>
</div>

<script>
// Global function to update arrow based on CSS variable
window.updateSelectArrowImage = function() {
  const computedStyle = getComputedStyle(document.documentElement);
  const colorTextBright = computedStyle.getPropertyValue('--color-text-bright').trim();

  document.querySelectorAll('.custom-float-select').forEach(function(select) {
    if (colorTextBright === '#F0DAC6') {
      select.style.backgroundImage = 'url(/static/images/icons8-arrow-down-64-dark.png)';
    } else {
      select.style.backgroundImage = 'url(/static/images/icons8-arrow-down-64.png)';
    }
  });
};

document.addEventListener("DOMContentLoaded", function() {
  // Function to update arrow based on CSS variable
  function updateArrowImage() {
    window.updateSelectArrowImage();
  }

  // For all custom-float-select elements on the page
  document.querySelectorAll('.custom-float-select').forEach(function(select) {
    const label = select.nextElementSibling && select.nextElementSibling.classList.contains('custom-float-label') ? select.nextElementSibling : null;
    function updateLabel() {
      if (select && label) {
        if (select.value && select.value !== "") {
          label.classList.add('float-label');
        } else {
          label.classList.remove('float-label');
        }
      }
    }
    // Set value attribute for CSS compatibility
    select.setAttribute('value', select.value);
    updateLabel();
    select.addEventListener('change', function() {
      select.setAttribute('value', select.value);
      updateLabel();
    });
    // Also update label on page load in case value is set by browser autofill or server
    updateLabel();
  });

  // Update arrow images on page load
  updateArrowImage();
});
</script>

